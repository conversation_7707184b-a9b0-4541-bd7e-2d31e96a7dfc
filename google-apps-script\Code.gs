/**
 * Automated Review Collection System for Google Sheets
 * Collects reviews from multiple platforms and updates Google Sheets
 */

// Configuration - Update these with your actual values
const CONFIG = {
  // Google My Business
  GMB_LOCATION_ID: 'your_gmb_location_id',
  GMB_API_KEY: 'your_gmb_api_key',
  
  // Trustpilot
  TRUSTPILOT_API_KEY: 'your_trustpilot_api_key',
  TRUSTPILOT_BUSINESS_UNIT_ID: 'your_business_unit_id',
  
  // App Store Connect (if you have access)
  APP_STORE_KEY_ID: 'your_app_store_key_id',
  APP_STORE_ISSUER_ID: 'your_app_store_issuer_id',
  APP_STORE_PRIVATE_KEY: 'your_private_key',
  
  // Google Play Console (if you have access)
  PLAY_CONSOLE_PACKAGE_NAME: 'your_app_package_name',
  
  // Sheet configuration
  SPREADSHEET_ID: 'your_spreadsheet_id',
  SHEETS: {
    GOOGLE_REVIEWS: 'Google Reviews',
    TRUSTPILOT: 'Trustpilot',
    APP_STORE: 'App Store',
    GOOGLE_PLAY: 'Google Play',
    GLASSDOOR: 'Glassdoor',
    SUMMARY: 'Summary'
  }
};

/**
 * Main function to collect all reviews
 */
function collectAllReviews() {
  try {
    console.log('Starting review collection...');
    
    // Collect from each platform
    collectGoogleReviews();
    collectTrustpilotReviews();
    collectAppStoreReviews();
    collectGooglePlayReviews();
    collectGlassdoorReviews();
    
    // Update summary sheet
    updateSummarySheet();
    
    console.log('Review collection completed successfully');
    
    // Send notification
    sendNotification('Review collection completed successfully');
    
  } catch (error) {
    console.error('Error in collectAllReviews:', error);
    sendNotification('Error in review collection: ' + error.message);
  }
}

/**
 * Collect Google My Business reviews
 */
function collectGoogleReviews() {
  try {
    const url = `https://mybusiness.googleapis.com/v4/accounts/${CONFIG.GMB_LOCATION_ID}/locations/${CONFIG.GMB_LOCATION_ID}/reviews?key=${CONFIG.GMB_API_KEY}`;
    
    const response = UrlFetchApp.fetch(url, {
      method: 'GET',
      headers: {
        'Authorization': 'Bearer ' + getGoogleAccessToken()
      }
    });
    
    if (response.getResponseCode() === 200) {
      const data = JSON.parse(response.getContentText());
      updateSheet(CONFIG.SHEETS.GOOGLE_REVIEWS, data.reviews, 'google');
    }
  } catch (error) {
    console.error('Error collecting Google reviews:', error);
  }
}

/**
 * Collect Trustpilot reviews
 */
function collectTrustpilotReviews() {
  try {
    const url = `https://api.trustpilot.com/v1/business-units/${CONFIG.TRUSTPILOT_BUSINESS_UNIT_ID}/reviews`;
    
    const response = UrlFetchApp.fetch(url, {
      method: 'GET',
      headers: {
        'ApiKey': CONFIG.TRUSTPILOT_API_KEY
      }
    });
    
    if (response.getResponseCode() === 200) {
      const data = JSON.parse(response.getContentText());
      updateSheet(CONFIG.SHEETS.TRUSTPILOT, data.reviews, 'trustpilot');
    }
  } catch (error) {
    console.error('Error collecting Trustpilot reviews:', error);
  }
}

/**
 * Collect App Store reviews (requires App Store Connect API access)
 */
function collectAppStoreReviews() {
  try {
    // Note: This requires proper JWT token generation for App Store Connect API
    // Implementation would need JWT library and proper authentication
    console.log('App Store review collection requires additional setup');
    
    // Alternative: Use third-party service or web scraping
    collectAppStoreReviewsAlternative();
  } catch (error) {
    console.error('Error collecting App Store reviews:', error);
  }
}

/**
 * Alternative method for App Store reviews using RSS feed
 */
function collectAppStoreReviewsAlternative() {
  try {
    // App Store RSS feed for reviews (replace with your app ID)
    const appId = 'your_app_id';
    const url = `https://itunes.apple.com/rss/customerreviews/page=1/id=${appId}/sortby=mostrecent/xml`;
    
    const response = UrlFetchApp.fetch(url);
    if (response.getResponseCode() === 200) {
      const xmlContent = response.getContentText();
      const reviews = parseAppStoreXML(xmlContent);
      updateSheet(CONFIG.SHEETS.APP_STORE, reviews, 'appstore');
    }
  } catch (error) {
    console.error('Error collecting App Store reviews (alternative):', error);
  }
}

/**
 * Collect Google Play Store reviews
 */
function collectGooglePlayReviews() {
  try {
    // Note: Google Play Developer API has limited access to reviews
    // Alternative: Use third-party services or web scraping
    console.log('Google Play review collection requires Play Console API access');
    
    // Alternative implementation using third-party service
    collectGooglePlayReviewsAlternative();
  } catch (error) {
    console.error('Error collecting Google Play reviews:', error);
  }
}

/**
 * Alternative method for Google Play reviews
 */
function collectGooglePlayReviewsAlternative() {
  try {
    // This would typically use a third-party service or web scraping
    // For demonstration, we'll create a placeholder
    console.log('Google Play reviews require alternative collection method');
  } catch (error) {
    console.error('Error in Google Play alternative collection:', error);
  }
}

/**
 * Collect Glassdoor reviews (requires web scraping)
 */
function collectGlassdoorReviews() {
  try {
    // Glassdoor doesn't have a public API
    // This would require web scraping, which has limitations in Apps Script
    console.log('Glassdoor reviews require web scraping - consider using external service');
  } catch (error) {
    console.error('Error collecting Glassdoor reviews:', error);
  }
}

/**
 * Update a specific sheet with review data
 */
function updateSheet(sheetName, reviews, platform) {
  try {
    const spreadsheet = SpreadsheetApp.openById(CONFIG.SPREADSHEET_ID);
    let sheet = spreadsheet.getSheetByName(sheetName);
    
    if (!sheet) {
      sheet = spreadsheet.insertSheet(sheetName);
      // Add headers
      const headers = ['Date', 'Rating', 'Review Text', 'Reviewer Name', 'Platform', 'Source URL', 'Sentiment'];
      sheet.getRange(1, 1, 1, headers.length).setValues([headers]);
    }
    
    if (reviews && reviews.length > 0) {
      const formattedReviews = formatReviews(reviews, platform);
      const lastRow = sheet.getLastRow();
      
      // Check for duplicates before adding
      const newReviews = filterNewReviews(sheet, formattedReviews);
      
      if (newReviews.length > 0) {
        sheet.getRange(lastRow + 1, 1, newReviews.length, newReviews[0].length).setValues(newReviews);
        console.log(`Added ${newReviews.length} new reviews to ${sheetName}`);
      }
    }
  } catch (error) {
    console.error(`Error updating sheet ${sheetName}:`, error);
  }
}

/**
 * Format reviews for consistent sheet structure
 */
function formatReviews(reviews, platform) {
  return reviews.map(review => {
    let date, rating, text, reviewer, sourceUrl;
    
    switch (platform) {
      case 'google':
        date = review.createTime || new Date();
        rating = review.starRating || 0;
        text = review.comment || '';
        reviewer = review.reviewer?.displayName || 'Anonymous';
        sourceUrl = review.reviewReply?.comment || '';
        break;
        
      case 'trustpilot':
        date = review.createdAt || new Date();
        rating = review.stars || 0;
        text = review.text || '';
        reviewer = review.consumer?.displayName || 'Anonymous';
        sourceUrl = review.url || '';
        break;
        
      case 'appstore':
        date = review.date || new Date();
        rating = review.rating || 0;
        text = review.text || '';
        reviewer = review.author || 'Anonymous';
        sourceUrl = '';
        break;
        
      default:
        date = new Date();
        rating = 0;
        text = '';
        reviewer = 'Unknown';
        sourceUrl = '';
    }
    
    const sentiment = analyzeSentiment(text);
    
    return [
      new Date(date),
      rating,
      text,
      reviewer,
      platform,
      sourceUrl,
      sentiment
    ];
  });
}

/**
 * Filter out duplicate reviews
 */
function filterNewReviews(sheet, newReviews) {
  const existingData = sheet.getDataRange().getValues();
  const existingReviews = new Set();
  
  // Create a set of existing review identifiers
  for (let i = 1; i < existingData.length; i++) {
    const identifier = `${existingData[i][0]}_${existingData[i][2]}_${existingData[i][3]}`;
    existingReviews.add(identifier);
  }
  
  // Filter out duplicates
  return newReviews.filter(review => {
    const identifier = `${review[0]}_${review[2]}_${review[3]}`;
    return !existingReviews.has(identifier);
  });
}

/**
 * Simple sentiment analysis
 */
function analyzeSentiment(text) {
  const positiveWords = ['good', 'great', 'excellent', 'amazing', 'love', 'perfect', 'awesome'];
  const negativeWords = ['bad', 'terrible', 'awful', 'hate', 'worst', 'horrible', 'disappointing'];
  
  const lowerText = text.toLowerCase();
  let positiveCount = 0;
  let negativeCount = 0;
  
  positiveWords.forEach(word => {
    if (lowerText.includes(word)) positiveCount++;
  });
  
  negativeWords.forEach(word => {
    if (lowerText.includes(word)) negativeCount++;
  });
  
  if (positiveCount > negativeCount) return 'Positive';
  if (negativeCount > positiveCount) return 'Negative';
  return 'Neutral';
}

/**
 * Update summary sheet with aggregated data
 */
function updateSummarySheet() {
  try {
    const spreadsheet = SpreadsheetApp.openById(CONFIG.SPREADSHEET_ID);
    let summarySheet = spreadsheet.getSheetByName(CONFIG.SHEETS.SUMMARY);
    
    if (!summarySheet) {
      summarySheet = spreadsheet.insertSheet(CONFIG.SHEETS.SUMMARY);
    }
    
    // Clear existing content
    summarySheet.clear();
    
    // Add headers
    const headers = ['Platform', 'Total Reviews', 'Average Rating', 'Positive', 'Negative', 'Neutral', 'Last Updated'];
    summarySheet.getRange(1, 1, 1, headers.length).setValues([headers]);
    
    // Calculate summary for each platform
    const platforms = Object.values(CONFIG.SHEETS).filter(sheet => sheet !== CONFIG.SHEETS.SUMMARY);
    const summaryData = [];
    
    platforms.forEach(platform => {
      const sheet = spreadsheet.getSheetByName(platform);
      if (sheet && sheet.getLastRow() > 1) {
        const data = sheet.getDataRange().getValues();
        const reviews = data.slice(1); // Skip header
        
        const totalReviews = reviews.length;
        const avgRating = reviews.reduce((sum, review) => sum + (review[1] || 0), 0) / totalReviews;
        const positive = reviews.filter(review => review[6] === 'Positive').length;
        const negative = reviews.filter(review => review[6] === 'Negative').length;
        const neutral = reviews.filter(review => review[6] === 'Neutral').length;
        
        summaryData.push([
          platform,
          totalReviews,
          Math.round(avgRating * 100) / 100,
          positive,
          negative,
          neutral,
          new Date()
        ]);
      }
    });
    
    if (summaryData.length > 0) {
      summarySheet.getRange(2, 1, summaryData.length, summaryData[0].length).setValues(summaryData);
    }
  } catch (error) {
    console.error('Error updating summary sheet:', error);
  }
}

/**
 * Get Google OAuth access token
 */
function getGoogleAccessToken() {
  // This would need to be implemented based on your OAuth setup
  // For now, return a placeholder
  return 'your_access_token';
}

/**
 * Parse App Store XML feed
 */
function parseAppStoreXML(xmlContent) {
  // Basic XML parsing for App Store RSS feed
  // This is a simplified implementation
  const reviews = [];
  
  try {
    // Parse XML and extract review data
    // Implementation would depend on the actual XML structure
    console.log('Parsing App Store XML feed');
  } catch (error) {
    console.error('Error parsing App Store XML:', error);
  }
  
  return reviews;
}

/**
 * Send notification about collection status
 */
function sendNotification(message) {
  try {
    // Send email notification
    const email = '<EMAIL>';
    const subject = 'Review Collection Status';
    
    MailApp.sendEmail(email, subject, message);
  } catch (error) {
    console.error('Error sending notification:', error);
  }
}

/**
 * Set up time-based triggers
 */
function setupTriggers() {
  // Delete existing triggers
  const triggers = ScriptApp.getProjectTriggers();
  triggers.forEach(trigger => ScriptApp.deleteTrigger(trigger));
  
  // Create daily trigger
  ScriptApp.newTrigger('collectAllReviews')
    .timeBased()
    .everyDays(1)
    .atHour(9) // Run at 9 AM daily
    .create();
  
  console.log('Triggers set up successfully');
}

/**
 * Webhook endpoint for real-time updates
 */
function doPost(e) {
  try {
    const data = JSON.parse(e.postData.contents);
    
    // Handle webhook data based on source
    if (data.source === 'trustpilot') {
      handleTrustpilotWebhook(data);
    } else if (data.source === 'google') {
      handleGoogleWebhook(data);
    }
    
    return ContentService.createTextOutput('OK');
  } catch (error) {
    console.error('Error handling webhook:', error);
    return ContentService.createTextOutput('Error');
  }
}

/**
 * Handle Trustpilot webhook
 */
function handleTrustpilotWebhook(data) {
  // Process Trustpilot webhook data
  console.log('Processing Trustpilot webhook');
}

/**
 * Handle Google webhook
 */
function handleGoogleWebhook(data) {
  // Process Google webhook data
  console.log('Processing Google webhook');
}
