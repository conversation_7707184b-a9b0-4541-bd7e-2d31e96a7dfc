# Automated Review Collection System Setup Guide

## Overview
This system automatically collects reviews from multiple platforms and updates your Google Sheets daily. It also supports real-time updates via webhooks when new reviews are added.

## Supported Platforms
- ✅ **Google My Business Reviews** (via API)
- ✅ **Trustpilot Reviews** (via API)
- ⚠️ **App Store Reviews** (via RSS feed - limited data)
- ⚠️ **Google Play Store Reviews** (requires Play Console API access)
- ❌ **Glassdoor Reviews** (no public API - requires third-party service)

## Setup Instructions

### 1. Google Apps Script Setup

1. **Create a new Google Apps Script project:**
   - Go to [script.google.com](https://script.google.com)
   - Click "New Project"
   - Replace the default code with the code from `google-apps-script/Code.gs`

2. **Enable required APIs:**
   - Go to Google Cloud Console
   - Enable the following APIs:
     - Google My Business API
     - Google Sheets API
     - Gmail API (for notifications)

### 2. Platform API Setup

#### Google My Business
1. Go to [Google My Business API](https://developers.google.com/my-business)
2. Create credentials and get your API key
3. Find your location ID from Google My Business dashboard
4. Update `CONFIG.GMB_LOCATION_ID` and `CONFIG.GMB_API_KEY` in the script

#### Trustpilot
1. Go to [Trustpilot Developer Portal](https://developers.trustpilot.com/)
2. Create an account and get API key
3. Find your Business Unit ID
4. Update `CONFIG.TRUSTPILOT_API_KEY` and `CONFIG.TRUSTPILOT_BUSINESS_UNIT_ID`

#### App Store (Optional)
1. For basic reviews, find your App ID from App Store Connect
2. Update the `appId` variable in `collectAppStoreReviewsAlternative()`
3. For advanced features, set up App Store Connect API (requires paid developer account)

#### Google Play Store (Optional)
1. Requires Google Play Console API access
2. Set up service account in Google Cloud Console
3. Grant access in Play Console
4. Update `CONFIG.PLAY_CONSOLE_PACKAGE_NAME`

### 3. Google Sheets Setup

1. **Create your Google Sheet:**
   - Create a new Google Sheet
   - Copy the Sheet ID from the URL
   - Update `CONFIG.SPREADSHEET_ID` in the script

2. **Sheet Structure:**
   The script will automatically create these sheets:
   - `Google Reviews`
   - `Trustpilot`
   - `App Store`
   - `Google Play`
   - `Glassdoor`
   - `Summary`

### 4. Configure Automation

#### Daily Updates
Run this function once to set up daily automation:
```javascript
setupTriggers()
```

This will:
- Delete any existing triggers
- Create a daily trigger that runs at 9 AM
- Collect reviews from all platforms
- Update the summary sheet
- Send email notifications

#### Real-time Updates (Webhooks)
1. **Deploy as Web App:**
   - In Apps Script, click "Deploy" > "New Deployment"
   - Choose "Web app" as type
   - Set execute as "Me"
   - Set access to "Anyone"
   - Copy the web app URL

2. **Configure Platform Webhooks:**
   - **Trustpilot:** Set up webhook in Trustpilot dashboard pointing to your web app URL
   - **Google My Business:** Configure webhook notifications (if available)

### 5. Third-party Services (Alternative)

For platforms without APIs or with limited access, consider these services:

#### Outscraper
- Supports Google Reviews, Trustpilot, App Store, Google Play
- API available for integration
- Paid service with free tier

#### ReviewFlowz
- Supports multiple platforms
- Webhook notifications
- Slack/email integration

#### Apify
- Web scraping actors for various platforms
- API integration available
- Pay-per-use model

## Configuration

### Update CONFIG object in Code.gs:

```javascript
const CONFIG = {
  // Your API keys and IDs
  GMB_LOCATION_ID: 'your_actual_location_id',
  GMB_API_KEY: 'your_actual_api_key',
  TRUSTPILOT_API_KEY: 'your_actual_trustpilot_key',
  TRUSTPILOT_BUSINESS_UNIT_ID: 'your_actual_business_unit_id',
  
  // Your Google Sheet ID
  SPREADSHEET_ID: 'your_actual_spreadsheet_id',
  
  // Email for notifications
  NOTIFICATION_EMAIL: '<EMAIL>'
};
```

## Testing

1. **Test individual functions:**
   ```javascript
   collectGoogleReviews()
   collectTrustpilotReviews()
   ```

2. **Test full collection:**
   ```javascript
   collectAllReviews()
   ```

3. **Check logs:**
   - View > Logs in Apps Script editor
   - Check for any errors or warnings

## Monitoring

### Email Notifications
- Success/failure notifications sent daily
- Error alerts for API issues
- Summary reports

### Sheet Monitoring
- Check the "Summary" sheet for overview
- Monitor "Last Updated" column
- Review sentiment analysis

## Troubleshooting

### Common Issues

1. **API Rate Limits:**
   - Add delays between API calls
   - Implement exponential backoff

2. **Authentication Errors:**
   - Check API keys are valid
   - Verify OAuth tokens are refreshed

3. **Duplicate Reviews:**
   - The system filters duplicates automatically
   - Based on date, text, and reviewer name

4. **Missing Reviews:**
   - Check API quotas and limits
   - Verify platform-specific settings

### Error Handling
- All functions include try-catch blocks
- Errors are logged and emailed
- System continues with other platforms if one fails

## Advanced Features

### Custom Sentiment Analysis
- Modify `analyzeSentiment()` function
- Integrate with Google Cloud Natural Language API
- Add custom keywords for your industry

### Custom Notifications
- Slack integration
- SMS alerts
- Dashboard webhooks

### Data Export
- Automatic CSV exports
- Integration with BI tools
- API endpoints for external access

## Security Considerations

1. **API Keys:**
   - Store in Apps Script Properties Service
   - Never commit to version control
   - Rotate regularly

2. **Sheet Access:**
   - Limit sharing permissions
   - Use service accounts for automation
   - Monitor access logs

3. **Webhook Security:**
   - Validate webhook signatures
   - Use HTTPS only
   - Implement rate limiting

## Support

For issues or questions:
1. Check the Apps Script logs
2. Verify API documentation for each platform
3. Test individual functions separately
4. Check platform-specific status pages

## Next Steps

1. Set up the basic Google Apps Script
2. Configure at least one platform (recommend starting with Trustpilot)
3. Test the daily automation
4. Gradually add other platforms
5. Set up webhooks for real-time updates
6. Consider third-party services for platforms without APIs
