#!/usr/bin/env python3
"""
Advanced Review Collection System
Alternative Python implementation with better API handling and more features
"""

import os
import json
import time
import logging
import requests
import pandas as pd
from datetime import datetime, timedelta
from typing import List, Dict, Optional
import gspread
from google.oauth2.service_account import Credentials
from textblob import TextBlob
import schedule
from flask import Flask, request, jsonify

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler('review_collector.log'),
        logging.StreamHandler()
    ]
)
logger = logging.getLogger(__name__)

class ReviewCollector:
    def __init__(self, config_file='config.json'):
        """Initialize the review collector with configuration"""
        self.config = self.load_config(config_file)
        self.setup_google_sheets()
        
    def load_config(self, config_file: str) -> Dict:
        """Load configuration from JSON file"""
        try:
            with open(config_file, 'r') as f:
                return json.load(f)
        except FileNotFoundError:
            logger.error(f"Config file {config_file} not found")
            return self.get_default_config()
    
    def get_default_config(self) -> Dict:
        """Return default configuration"""
        return {
            "google_sheets": {
                "spreadsheet_id": "",
                "credentials_file": "credentials.json"
            },
            "apis": {
                "google_my_business": {
                    "api_key": "",
                    "location_id": ""
                },
                "trustpilot": {
                    "api_key": "",
                    "business_unit_id": ""
                },
                "app_store": {
                    "app_id": "",
                    "country": "us"
                },
                "google_play": {
                    "package_name": "",
                    "service_account_file": ""
                }
            },
            "notifications": {
                "email": "",
                "slack_webhook": "",
                "discord_webhook": ""
            },
            "settings": {
                "update_interval_hours": 24,
                "max_reviews_per_platform": 100,
                "sentiment_analysis": True
            }
        }
    
    def setup_google_sheets(self):
        """Setup Google Sheets connection"""
        try:
            scope = [
                'https://spreadsheets.google.com/feeds',
                'https://www.googleapis.com/auth/drive'
            ]
            
            creds = Credentials.from_service_account_file(
                self.config['google_sheets']['credentials_file'],
                scopes=scope
            )
            
            self.gc = gspread.authorize(creds)
            self.spreadsheet = self.gc.open_by_key(
                self.config['google_sheets']['spreadsheet_id']
            )
            logger.info("Google Sheets connection established")
            
        except Exception as e:
            logger.error(f"Failed to setup Google Sheets: {e}")
            self.gc = None
            self.spreadsheet = None
    
    def collect_google_reviews(self) -> List[Dict]:
        """Collect Google My Business reviews"""
        reviews = []
        try:
            api_key = self.config['apis']['google_my_business']['api_key']
            location_id = self.config['apis']['google_my_business']['location_id']
            
            url = f"https://mybusiness.googleapis.com/v4/accounts/{location_id}/locations/{location_id}/reviews"
            headers = {'Authorization': f'Bearer {api_key}'}
            
            response = requests.get(url, headers=headers)
            
            if response.status_code == 200:
                data = response.json()
                for review in data.get('reviews', []):
                    reviews.append({
                        'platform': 'Google',
                        'date': review.get('createTime', ''),
                        'rating': review.get('starRating', 0),
                        'text': review.get('comment', ''),
                        'reviewer': review.get('reviewer', {}).get('displayName', 'Anonymous'),
                        'url': '',
                        'sentiment': self.analyze_sentiment(review.get('comment', ''))
                    })
                    
                logger.info(f"Collected {len(reviews)} Google reviews")
            else:
                logger.error(f"Google API error: {response.status_code}")
                
        except Exception as e:
            logger.error(f"Error collecting Google reviews: {e}")
            
        return reviews
    
    def collect_trustpilot_reviews(self) -> List[Dict]:
        """Collect Trustpilot reviews"""
        reviews = []
        try:
            api_key = self.config['apis']['trustpilot']['api_key']
            business_unit_id = self.config['apis']['trustpilot']['business_unit_id']
            
            url = f"https://api.trustpilot.com/v1/business-units/{business_unit_id}/reviews"
            headers = {'ApiKey': api_key}
            params = {
                'perPage': self.config['settings']['max_reviews_per_platform'],
                'orderBy': 'createdat.desc'
            }
            
            response = requests.get(url, headers=headers, params=params)
            
            if response.status_code == 200:
                data = response.json()
                for review in data.get('reviews', []):
                    reviews.append({
                        'platform': 'Trustpilot',
                        'date': review.get('createdAt', ''),
                        'rating': review.get('stars', 0),
                        'text': review.get('text', ''),
                        'reviewer': review.get('consumer', {}).get('displayName', 'Anonymous'),
                        'url': review.get('url', ''),
                        'sentiment': self.analyze_sentiment(review.get('text', ''))
                    })
                    
                logger.info(f"Collected {len(reviews)} Trustpilot reviews")
            else:
                logger.error(f"Trustpilot API error: {response.status_code}")
                
        except Exception as e:
            logger.error(f"Error collecting Trustpilot reviews: {e}")
            
        return reviews
    
    def collect_app_store_reviews(self) -> List[Dict]:
        """Collect App Store reviews using RSS feed"""
        reviews = []
        try:
            app_id = self.config['apis']['app_store']['app_id']
            country = self.config['apis']['app_store']['country']
            
            url = f"https://itunes.apple.com/{country}/rss/customerreviews/page=1/id={app_id}/sortby=mostrecent/json"
            
            response = requests.get(url)
            
            if response.status_code == 200:
                data = response.json()
                entries = data.get('feed', {}).get('entry', [])
                
                for entry in entries[1:]:  # Skip first entry (app info)
                    rating_text = entry.get('im:rating', {}).get('label', '0')
                    rating = int(rating_text) if rating_text.isdigit() else 0
                    
                    reviews.append({
                        'platform': 'App Store',
                        'date': entry.get('updated', {}).get('label', ''),
                        'rating': rating,
                        'text': entry.get('content', {}).get('label', ''),
                        'reviewer': entry.get('author', {}).get('name', {}).get('label', 'Anonymous'),
                        'url': '',
                        'sentiment': self.analyze_sentiment(entry.get('content', {}).get('label', ''))
                    })
                    
                logger.info(f"Collected {len(reviews)} App Store reviews")
            else:
                logger.error(f"App Store API error: {response.status_code}")
                
        except Exception as e:
            logger.error(f"Error collecting App Store reviews: {e}")
            
        return reviews
    
    def collect_google_play_reviews(self) -> List[Dict]:
        """Collect Google Play Store reviews"""
        reviews = []
        try:
            # This would require Google Play Developer API setup
            # For now, we'll use a placeholder or third-party service
            logger.info("Google Play reviews collection requires Play Developer API")
            
            # Alternative: Use third-party service like Outscraper
            # reviews = self.collect_play_reviews_outscraper()
            
        except Exception as e:
            logger.error(f"Error collecting Google Play reviews: {e}")
            
        return reviews
    
    def collect_glassdoor_reviews(self) -> List[Dict]:
        """Collect Glassdoor reviews (requires third-party service)"""
        reviews = []
        try:
            # Glassdoor doesn't have a public API
            # Would need to use web scraping service
            logger.info("Glassdoor reviews require third-party scraping service")
            
        except Exception as e:
            logger.error(f"Error collecting Glassdoor reviews: {e}")
            
        return reviews
    
    def analyze_sentiment(self, text: str) -> str:
        """Analyze sentiment of review text"""
        if not self.config['settings']['sentiment_analysis'] or not text:
            return 'Neutral'
            
        try:
            blob = TextBlob(text)
            polarity = blob.sentiment.polarity
            
            if polarity > 0.1:
                return 'Positive'
            elif polarity < -0.1:
                return 'Negative'
            else:
                return 'Neutral'
                
        except Exception as e:
            logger.error(f"Error analyzing sentiment: {e}")
            return 'Neutral'
    
    def update_google_sheet(self, sheet_name: str, reviews: List[Dict]):
        """Update Google Sheet with review data"""
        if not self.spreadsheet:
            logger.error("Google Sheets not configured")
            return
            
        try:
            # Get or create worksheet
            try:
                worksheet = self.spreadsheet.worksheet(sheet_name)
            except gspread.WorksheetNotFound:
                worksheet = self.spreadsheet.add_worksheet(
                    title=sheet_name, 
                    rows=1000, 
                    cols=10
                )
                # Add headers
                headers = ['Date', 'Rating', 'Review Text', 'Reviewer', 'Platform', 'URL', 'Sentiment', 'Collected At']
                worksheet.append_row(headers)
            
            # Convert reviews to rows
            rows = []
            for review in reviews:
                rows.append([
                    review['date'],
                    review['rating'],
                    review['text'][:1000],  # Limit text length
                    review['reviewer'],
                    review['platform'],
                    review['url'],
                    review['sentiment'],
                    datetime.now().isoformat()
                ])
            
            # Check for duplicates and add new reviews
            existing_data = worksheet.get_all_values()[1:]  # Skip header
            existing_reviews = set()
            
            for row in existing_data:
                if len(row) >= 4:
                    identifier = f"{row[0]}_{row[2][:50]}_{row[3]}"
                    existing_reviews.add(identifier)
            
            new_rows = []
            for row in rows:
                identifier = f"{row[0]}_{row[2][:50]}_{row[3]}"
                if identifier not in existing_reviews:
                    new_rows.append(row)
            
            if new_rows:
                worksheet.append_rows(new_rows)
                logger.info(f"Added {len(new_rows)} new reviews to {sheet_name}")
            else:
                logger.info(f"No new reviews found for {sheet_name}")
                
        except Exception as e:
            logger.error(f"Error updating sheet {sheet_name}: {e}")
    
    def collect_all_reviews(self):
        """Collect reviews from all platforms"""
        logger.info("Starting review collection from all platforms")
        
        all_reviews = {
            'Google Reviews': self.collect_google_reviews(),
            'Trustpilot': self.collect_trustpilot_reviews(),
            'App Store': self.collect_app_store_reviews(),
            'Google Play': self.collect_google_play_reviews(),
            'Glassdoor': self.collect_glassdoor_reviews()
        }
        
        # Update Google Sheets
        for platform, reviews in all_reviews.items():
            if reviews:
                self.update_google_sheet(platform, reviews)
        
        # Update summary
        self.update_summary_sheet(all_reviews)
        
        # Send notifications
        self.send_notification(all_reviews)
        
        logger.info("Review collection completed")
    
    def update_summary_sheet(self, all_reviews: Dict[str, List[Dict]]):
        """Update summary sheet with aggregated data"""
        if not self.spreadsheet:
            return
            
        try:
            try:
                worksheet = self.spreadsheet.worksheet('Summary')
                worksheet.clear()
            except gspread.WorksheetNotFound:
                worksheet = self.spreadsheet.add_worksheet(title='Summary', rows=100, cols=10)
            
            # Headers
            headers = ['Platform', 'Total Reviews', 'Average Rating', 'Positive', 'Negative', 'Neutral', 'Last Updated']
            worksheet.append_row(headers)
            
            # Summary data
            for platform, reviews in all_reviews.items():
                if reviews:
                    total = len(reviews)
                    avg_rating = sum(r['rating'] for r in reviews) / total if total > 0 else 0
                    positive = sum(1 for r in reviews if r['sentiment'] == 'Positive')
                    negative = sum(1 for r in reviews if r['sentiment'] == 'Negative')
                    neutral = sum(1 for r in reviews if r['sentiment'] == 'Neutral')
                    
                    worksheet.append_row([
                        platform,
                        total,
                        round(avg_rating, 2),
                        positive,
                        negative,
                        neutral,
                        datetime.now().strftime('%Y-%m-%d %H:%M:%S')
                    ])
                    
            logger.info("Summary sheet updated")
            
        except Exception as e:
            logger.error(f"Error updating summary sheet: {e}")
    
    def send_notification(self, all_reviews: Dict[str, List[Dict]]):
        """Send notification about collection results"""
        try:
            total_reviews = sum(len(reviews) for reviews in all_reviews.values())
            message = f"Review collection completed. Total new reviews: {total_reviews}"
            
            # Add platform breakdown
            for platform, reviews in all_reviews.items():
                if reviews:
                    message += f"\n{platform}: {len(reviews)} reviews"
            
            # Send email notification (implement based on your email service)
            # self.send_email_notification(message)
            
            # Send Slack notification
            if self.config['notifications']['slack_webhook']:
                self.send_slack_notification(message)
            
            logger.info("Notifications sent")
            
        except Exception as e:
            logger.error(f"Error sending notifications: {e}")
    
    def send_slack_notification(self, message: str):
        """Send Slack notification"""
        try:
            webhook_url = self.config['notifications']['slack_webhook']
            payload = {'text': message}
            
            response = requests.post(webhook_url, json=payload)
            if response.status_code == 200:
                logger.info("Slack notification sent")
            else:
                logger.error(f"Slack notification failed: {response.status_code}")
                
        except Exception as e:
            logger.error(f"Error sending Slack notification: {e}")

# Flask app for webhooks
app = Flask(__name__)
collector = ReviewCollector()

@app.route('/webhook/trustpilot', methods=['POST'])
def trustpilot_webhook():
    """Handle Trustpilot webhook"""
    try:
        data = request.json
        logger.info(f"Received Trustpilot webhook: {data}")
        
        # Process webhook data and update sheet
        # Implementation depends on Trustpilot webhook format
        
        return jsonify({'status': 'success'})
    except Exception as e:
        logger.error(f"Error handling Trustpilot webhook: {e}")
        return jsonify({'status': 'error'}), 500

@app.route('/webhook/google', methods=['POST'])
def google_webhook():
    """Handle Google My Business webhook"""
    try:
        data = request.json
        logger.info(f"Received Google webhook: {data}")
        
        # Process webhook data and update sheet
        # Implementation depends on Google webhook format
        
        return jsonify({'status': 'success'})
    except Exception as e:
        logger.error(f"Error handling Google webhook: {e}")
        return jsonify({'status': 'error'}), 500

def run_scheduler():
    """Run the scheduled review collection"""
    schedule.every().day.at("09:00").do(collector.collect_all_reviews)
    
    while True:
        schedule.run_pending()
        time.sleep(60)

if __name__ == '__main__':
    import sys
    
    if len(sys.argv) > 1:
        if sys.argv[1] == 'collect':
            collector.collect_all_reviews()
        elif sys.argv[1] == 'schedule':
            run_scheduler()
        elif sys.argv[1] == 'webhook':
            app.run(host='0.0.0.0', port=5000)
    else:
        print("Usage: python review_collector.py [collect|schedule|webhook]")
        print("  collect  - Run one-time collection")
        print("  schedule - Run scheduled collection")
        print("  webhook  - Start webhook server")
