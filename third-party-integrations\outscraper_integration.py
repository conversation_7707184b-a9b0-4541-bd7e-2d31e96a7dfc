#!/usr/bin/env python3
"""
Outscraper Integration for Review Collection
Handles platforms that don't have public APIs
"""

import os
import json
import time
import logging
from typing import List, Dict, Optional
import requests
from datetime import datetime

logger = logging.getLogger(__name__)

class OutscraperIntegration:
    """
    Integration with Outscraper API for collecting reviews
    from platforms without public APIs
    """
    
    def __init__(self, api_key: str):
        self.api_key = api_key
        self.base_url = "https://api.outscraper.com"
        self.headers = {
            'X-API-KEY': api_key,
            'Content-Type': 'application/json'
        }
    
    def get_google_reviews(self, business_url: str, limit: int = 100) -> List[Dict]:
        """
        Get Google Maps reviews using Outscraper
        
        Args:
            business_url: Google Maps business URL or place ID
            limit: Maximum number of reviews to fetch
            
        Returns:
            List of review dictionaries
        """
        try:
            endpoint = f"{self.base_url}/maps/reviews-v3"
            params = {
                'query': business_url,
                'limit': limit,
                'sort': 'newest',
                'language': 'en'
            }
            
            response = requests.get(endpoint, headers=self.headers, params=params)
            
            if response.status_code == 200:
                data = response.json()
                reviews = []
                
                for result in data:
                    if 'reviews_data' in result:
                        for review in result['reviews_data']:
                            reviews.append({
                                'platform': 'Google Maps',
                                'date': review.get('review_datetime_utc', ''),
                                'rating': review.get('review_rating', 0),
                                'text': review.get('review_text', ''),
                                'reviewer': review.get('author_title', 'Anonymous'),
                                'url': review.get('review_link', ''),
                                'likes': review.get('review_likes', 0),
                                'response': review.get('owner_answer', ''),
                                'response_date': review.get('owner_answer_timestamp_datetime_utc', '')
                            })
                
                logger.info(f"Collected {len(reviews)} Google reviews via Outscraper")
                return reviews
            else:
                logger.error(f"Outscraper API error: {response.status_code} - {response.text}")
                return []
                
        except Exception as e:
            logger.error(f"Error collecting Google reviews via Outscraper: {e}")
            return []
    
    def get_trustpilot_reviews(self, business_url: str, limit: int = 100) -> List[Dict]:
        """
        Get Trustpilot reviews using Outscraper
        
        Args:
            business_url: Trustpilot business URL
            limit: Maximum number of reviews to fetch
            
        Returns:
            List of review dictionaries
        """
        try:
            endpoint = f"{self.base_url}/trustpilot/reviews"
            params = {
                'query': business_url,
                'limit': limit
            }
            
            response = requests.get(endpoint, headers=self.headers, params=params)
            
            if response.status_code == 200:
                data = response.json()
                reviews = []
                
                for result in data:
                    if 'reviews_data' in result:
                        for review in result['reviews_data']:
                            reviews.append({
                                'platform': 'Trustpilot',
                                'date': review.get('review_date', ''),
                                'rating': review.get('review_rating', 0),
                                'text': review.get('review_text', ''),
                                'reviewer': review.get('reviewer_name', 'Anonymous'),
                                'url': review.get('review_url', ''),
                                'verified': review.get('verified_order', False),
                                'title': review.get('review_title', '')
                            })
                
                logger.info(f"Collected {len(reviews)} Trustpilot reviews via Outscraper")
                return reviews
            else:
                logger.error(f"Outscraper API error: {response.status_code} - {response.text}")
                return []
                
        except Exception as e:
            logger.error(f"Error collecting Trustpilot reviews via Outscraper: {e}")
            return []
    
    def get_app_store_reviews(self, app_id: str, country: str = 'us', limit: int = 100) -> List[Dict]:
        """
        Get App Store reviews using Outscraper
        
        Args:
            app_id: App Store app ID
            country: Country code (us, uk, etc.)
            limit: Maximum number of reviews to fetch
            
        Returns:
            List of review dictionaries
        """
        try:
            endpoint = f"{self.base_url}/app-store/reviews"
            params = {
                'app_id': app_id,
                'country': country,
                'limit': limit
            }
            
            response = requests.get(endpoint, headers=self.headers, params=params)
            
            if response.status_code == 200:
                data = response.json()
                reviews = []
                
                for result in data:
                    if 'reviews_data' in result:
                        for review in result['reviews_data']:
                            reviews.append({
                                'platform': 'App Store',
                                'date': review.get('review_date', ''),
                                'rating': review.get('review_rating', 0),
                                'text': review.get('review_text', ''),
                                'reviewer': review.get('reviewer_name', 'Anonymous'),
                                'version': review.get('app_version', ''),
                                'title': review.get('review_title', '')
                            })
                
                logger.info(f"Collected {len(reviews)} App Store reviews via Outscraper")
                return reviews
            else:
                logger.error(f"Outscraper API error: {response.status_code} - {response.text}")
                return []
                
        except Exception as e:
            logger.error(f"Error collecting App Store reviews via Outscraper: {e}")
            return []
    
    def get_google_play_reviews(self, package_name: str, limit: int = 100) -> List[Dict]:
        """
        Get Google Play Store reviews using Outscraper
        
        Args:
            package_name: Android app package name
            limit: Maximum number of reviews to fetch
            
        Returns:
            List of review dictionaries
        """
        try:
            endpoint = f"{self.base_url}/google-play/reviews"
            params = {
                'query': package_name,
                'limit': limit
            }
            
            response = requests.get(endpoint, headers=self.headers, params=params)
            
            if response.status_code == 200:
                data = response.json()
                reviews = []
                
                for result in data:
                    if 'reviews_data' in result:
                        for review in result['reviews_data']:
                            reviews.append({
                                'platform': 'Google Play',
                                'date': review.get('review_date', ''),
                                'rating': review.get('review_rating', 0),
                                'text': review.get('review_text', ''),
                                'reviewer': review.get('reviewer_name', 'Anonymous'),
                                'likes': review.get('review_likes', 0),
                                'response': review.get('developer_response', ''),
                                'response_date': review.get('developer_response_date', '')
                            })
                
                logger.info(f"Collected {len(reviews)} Google Play reviews via Outscraper")
                return reviews
            else:
                logger.error(f"Outscraper API error: {response.status_code} - {response.text}")
                return []
                
        except Exception as e:
            logger.error(f"Error collecting Google Play reviews via Outscraper: {e}")
            return []
    
    def get_glassdoor_reviews(self, company_url: str, limit: int = 100) -> List[Dict]:
        """
        Get Glassdoor reviews using Outscraper
        
        Args:
            company_url: Glassdoor company URL
            limit: Maximum number of reviews to fetch
            
        Returns:
            List of review dictionaries
        """
        try:
            endpoint = f"{self.base_url}/glassdoor/reviews"
            params = {
                'query': company_url,
                'limit': limit
            }
            
            response = requests.get(endpoint, headers=self.headers, params=params)
            
            if response.status_code == 200:
                data = response.json()
                reviews = []
                
                for result in data:
                    if 'reviews_data' in result:
                        for review in result['reviews_data']:
                            reviews.append({
                                'platform': 'Glassdoor',
                                'date': review.get('review_date', ''),
                                'rating': review.get('overall_rating', 0),
                                'text': review.get('review_text', ''),
                                'reviewer': review.get('reviewer_title', 'Anonymous'),
                                'pros': review.get('pros', ''),
                                'cons': review.get('cons', ''),
                                'advice': review.get('advice_to_management', ''),
                                'work_life_balance': review.get('work_life_balance_rating', 0),
                                'culture_values': review.get('culture_values_rating', 0),
                                'career_opportunities': review.get('career_opportunities_rating', 0),
                                'compensation': review.get('compensation_benefits_rating', 0),
                                'senior_management': review.get('senior_management_rating', 0)
                            })
                
                logger.info(f"Collected {len(reviews)} Glassdoor reviews via Outscraper")
                return reviews
            else:
                logger.error(f"Outscraper API error: {response.status_code} - {response.text}")
                return []
                
        except Exception as e:
            logger.error(f"Error collecting Glassdoor reviews via Outscraper: {e}")
            return []
    
    def check_quota(self) -> Dict:
        """
        Check remaining API quota
        
        Returns:
            Dictionary with quota information
        """
        try:
            endpoint = f"{self.base_url}/requests"
            response = requests.get(endpoint, headers=self.headers)
            
            if response.status_code == 200:
                return response.json()
            else:
                logger.error(f"Error checking quota: {response.status_code}")
                return {}
                
        except Exception as e:
            logger.error(f"Error checking quota: {e}")
            return {}

class ReviewFlowzIntegration:
    """
    Integration with ReviewFlowz for review monitoring and webhooks
    """
    
    def __init__(self, api_key: str):
        self.api_key = api_key
        self.base_url = "https://api.reviewflowz.com"
        self.headers = {
            'Authorization': f'Bearer {api_key}',
            'Content-Type': 'application/json'
        }
    
    def setup_webhook(self, webhook_url: str, platforms: List[str]) -> bool:
        """
        Set up webhook for real-time review notifications
        
        Args:
            webhook_url: Your webhook endpoint URL
            platforms: List of platforms to monitor
            
        Returns:
            True if successful, False otherwise
        """
        try:
            endpoint = f"{self.base_url}/webhooks"
            data = {
                'url': webhook_url,
                'platforms': platforms,
                'events': ['new_review', 'updated_review']
            }
            
            response = requests.post(endpoint, headers=self.headers, json=data)
            
            if response.status_code == 201:
                logger.info("Webhook set up successfully")
                return True
            else:
                logger.error(f"Error setting up webhook: {response.status_code}")
                return False
                
        except Exception as e:
            logger.error(f"Error setting up webhook: {e}")
            return False
    
    def get_recent_reviews(self, hours: int = 24) -> List[Dict]:
        """
        Get recent reviews from all monitored platforms
        
        Args:
            hours: Number of hours to look back
            
        Returns:
            List of recent reviews
        """
        try:
            endpoint = f"{self.base_url}/reviews/recent"
            params = {'hours': hours}
            
            response = requests.get(endpoint, headers=self.headers, params=params)
            
            if response.status_code == 200:
                data = response.json()
                return data.get('reviews', [])
            else:
                logger.error(f"Error getting recent reviews: {response.status_code}")
                return []
                
        except Exception as e:
            logger.error(f"Error getting recent reviews: {e}")
            return []

# Example usage
if __name__ == '__main__':
    # Outscraper example
    outscraper = OutscraperIntegration('your_outscraper_api_key')
    
    # Get Google reviews
    google_reviews = outscraper.get_google_reviews('https://maps.google.com/your-business')
    print(f"Found {len(google_reviews)} Google reviews")
    
    # Get App Store reviews
    app_reviews = outscraper.get_app_store_reviews('your_app_id')
    print(f"Found {len(app_reviews)} App Store reviews")
    
    # Check quota
    quota = outscraper.check_quota()
    print(f"Remaining quota: {quota}")
    
    # ReviewFlowz example
    reviewflowz = ReviewFlowzIntegration('your_reviewflowz_api_key')
    
    # Set up webhook
    webhook_success = reviewflowz.setup_webhook(
        'https://your-domain.com/webhook/reviewflowz',
        ['google', 'trustpilot', 'app_store']
    )
    
    if webhook_success:
        print("Webhook set up successfully")
    
    # Get recent reviews
    recent_reviews = reviewflowz.get_recent_reviews(24)
    print(f"Found {len(recent_reviews)} recent reviews")
