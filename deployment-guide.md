# Review Collection System - Deployment Guide

## Overview

This guide provides multiple deployment options for your automated review collection system:

1. **Google Apps Script** (Recommended for beginners)
2. **Python on Cloud** (Recommended for advanced features)
3. **Local Python Setup**
4. **Docker Deployment**
5. **Third-party Services**

## Option 1: Google Apps Script (Easiest)

### Pros:
- No server management required
- Free (within Google's quotas)
- Integrated with Google Sheets
- Built-in scheduling and webhooks

### Cons:
- Limited to Google's execution time limits
- Fewer customization options
- API rate limits

### Setup Steps:
1. Follow instructions in `setup-instructions.md`
2. Copy code from `google-apps-script/Code.gs`
3. Configure API keys in the script
4. Set up daily triggers
5. Deploy as web app for webhooks

## Option 2: Python on Google Cloud Platform

### Pros:
- More powerful and flexible
- Better error handling
- Advanced sentiment analysis
- Scalable

### Cons:
- Requires cloud setup
- May incur costs
- More complex setup

### Setup Steps:

#### 2.1 Prepare Your Environment
```bash
# Install Google Cloud SDK
curl https://sdk.cloud.google.com | bash
gcloud init

# Create a new project
gcloud projects create your-review-collector
gcloud config set project your-review-collector
```

#### 2.2 Deploy to Cloud Functions
```bash
# Create requirements.txt with minimal dependencies
echo "requests
gspread
google-auth
textblob
flask" > requirements.txt

# Deploy the function
gcloud functions deploy collect-reviews \
  --runtime python39 \
  --trigger-http \
  --entry-point collect_all_reviews \
  --memory 512MB \
  --timeout 540s
```

#### 2.3 Set up Cloud Scheduler
```bash
# Create a scheduled job
gcloud scheduler jobs create http review-collection-job \
  --schedule="0 9 * * *" \
  --uri="https://REGION-PROJECT.cloudfunctions.net/collect-reviews" \
  --http-method=GET
```

## Option 3: Local Python Setup

### Setup Steps:

#### 3.1 Install Python Dependencies
```bash
cd python-alternative
pip install -r requirements.txt
```

#### 3.2 Configure Credentials
1. **Google Sheets API:**
   - Go to Google Cloud Console
   - Enable Google Sheets API
   - Create service account
   - Download `credentials.json`

2. **Platform APIs:**
   - Update `config.json` with your API keys
   - See platform-specific setup in `setup-instructions.md`

#### 3.3 Run the Collector
```bash
# One-time collection
python review_collector.py collect

# Start scheduler (runs daily at 9 AM)
python review_collector.py schedule

# Start webhook server
python review_collector.py webhook
```

#### 3.4 Set up as System Service (Linux/Mac)
```bash
# Create systemd service file
sudo nano /etc/systemd/system/review-collector.service
```

Add this content:
```ini
[Unit]
Description=Review Collector Service
After=network.target

[Service]
Type=simple
User=your-username
WorkingDirectory=/path/to/python-alternative
ExecStart=/usr/bin/python3 review_collector.py schedule
Restart=always

[Install]
WantedBy=multi-user.target
```

Enable and start:
```bash
sudo systemctl enable review-collector
sudo systemctl start review-collector
```

## Option 4: Docker Deployment

### 4.1 Create Dockerfile
```dockerfile
FROM python:3.9-slim

WORKDIR /app

COPY requirements.txt .
RUN pip install --no-cache-dir -r requirements.txt

COPY . .

CMD ["python", "review_collector.py", "schedule"]
```

### 4.2 Build and Run
```bash
# Build image
docker build -t review-collector .

# Run container
docker run -d \
  --name review-collector \
  -v $(pwd)/config.json:/app/config.json \
  -v $(pwd)/credentials.json:/app/credentials.json \
  review-collector
```

### 4.3 Docker Compose Setup
```yaml
version: '3.8'
services:
  review-collector:
    build: .
    volumes:
      - ./config.json:/app/config.json
      - ./credentials.json:/app/credentials.json
      - ./logs:/app/logs
    restart: unless-stopped
    
  webhook-server:
    build: .
    command: python review_collector.py webhook
    ports:
      - "5000:5000"
    volumes:
      - ./config.json:/app/config.json
      - ./credentials.json:/app/credentials.json
    restart: unless-stopped
```

## Option 5: Third-party Services

### 5.1 Outscraper (Recommended)
- Supports all major platforms
- API integration available
- Handles rate limits and anti-bot measures

Setup:
```python
import outscraper

# Initialize client
client = outscraper.ApiClient(api_key='your_api_key')

# Get Google reviews
reviews = client.google_maps_reviews(['your_business_url'])

# Get App Store reviews
app_reviews = client.app_store_reviews(['your_app_id'])
```

### 5.2 ReviewFlowz
- Multi-platform support
- Webhook notifications
- Slack/email integration

### 5.3 Apify
- Web scraping actors
- API integration
- Pay-per-use model

## Webhook Setup for Real-time Updates

### 1. Expose Your Webhook Endpoint

#### Using ngrok (for testing):
```bash
# Install ngrok
npm install -g ngrok

# Expose local server
ngrok http 5000
```

#### Using Cloud Services:
- **Google Cloud Run**
- **AWS Lambda**
- **Heroku**
- **DigitalOcean Functions**

### 2. Configure Platform Webhooks

#### Trustpilot:
1. Go to Trustpilot Business Dashboard
2. Navigate to API settings
3. Add webhook URL: `https://your-domain.com/webhook/trustpilot`
4. Select events: New Review, Updated Review

#### Google My Business:
1. Set up Google My Business API
2. Configure Pub/Sub notifications
3. Point to your webhook endpoint

## Monitoring and Maintenance

### 1. Logging
- Check logs regularly for errors
- Set up log rotation
- Monitor API quotas and limits

### 2. Error Handling
- Implement retry logic for failed API calls
- Set up alerts for critical failures
- Monitor webhook delivery success

### 3. Data Quality
- Regular duplicate checks
- Sentiment analysis accuracy
- Review completeness validation

### 4. Security
- Rotate API keys regularly
- Use environment variables for secrets
- Implement webhook signature verification
- Set up rate limiting

## Cost Optimization

### 1. API Usage
- Monitor API call quotas
- Implement caching where possible
- Use incremental updates instead of full syncs

### 2. Cloud Resources
- Use appropriate instance sizes
- Implement auto-scaling
- Monitor and optimize costs

### 3. Third-party Services
- Compare pricing models
- Use free tiers where available
- Monitor usage to avoid overages

## Troubleshooting

### Common Issues:

1. **API Rate Limits**
   - Implement exponential backoff
   - Add delays between requests
   - Use multiple API keys if allowed

2. **Authentication Errors**
   - Check API key validity
   - Verify OAuth token refresh
   - Ensure proper scopes are granted

3. **Duplicate Reviews**
   - Improve duplicate detection logic
   - Use unique identifiers
   - Implement date-based filtering

4. **Missing Reviews**
   - Check API pagination
   - Verify date ranges
   - Monitor platform-specific limits

5. **Webhook Failures**
   - Verify endpoint accessibility
   - Check webhook signatures
   - Implement retry mechanisms

### Debug Commands:
```bash
# Check logs
tail -f review_collector.log

# Test API connections
python -c "from review_collector import ReviewCollector; c = ReviewCollector(); c.collect_trustpilot_reviews()"

# Verify Google Sheets connection
python -c "import gspread; gc = gspread.service_account('credentials.json'); print('Connected!')"
```

## Performance Optimization

### 1. Parallel Processing
```python
import concurrent.futures

def collect_all_reviews_parallel(self):
    with concurrent.futures.ThreadPoolExecutor(max_workers=4) as executor:
        futures = {
            executor.submit(self.collect_google_reviews): 'Google',
            executor.submit(self.collect_trustpilot_reviews): 'Trustpilot',
            executor.submit(self.collect_app_store_reviews): 'App Store',
        }
        
        results = {}
        for future in concurrent.futures.as_completed(futures):
            platform = futures[future]
            results[platform] = future.result()
        
        return results
```

### 2. Caching
```python
import redis
import json
from datetime import timedelta

class CachedReviewCollector(ReviewCollector):
    def __init__(self, *args, **kwargs):
        super().__init__(*args, **kwargs)
        self.redis_client = redis.Redis(host='localhost', port=6379, db=0)
    
    def get_cached_reviews(self, platform, cache_duration=3600):
        cached = self.redis_client.get(f"reviews:{platform}")
        if cached:
            return json.loads(cached)
        return None
    
    def cache_reviews(self, platform, reviews, cache_duration=3600):
        self.redis_client.setex(
            f"reviews:{platform}", 
            cache_duration, 
            json.dumps(reviews)
        )
```

### 3. Database Storage
For high-volume scenarios, consider using a database:
```python
import sqlalchemy as db
from sqlalchemy.orm import sessionmaker

# Define database models
class Review(db.Model):
    id = db.Column(db.Integer, primary_key=True)
    platform = db.Column(db.String(50))
    date = db.Column(db.DateTime)
    rating = db.Column(db.Integer)
    text = db.Column(db.Text)
    reviewer = db.Column(db.String(255))
    sentiment = db.Column(db.String(20))
    created_at = db.Column(db.DateTime, default=datetime.utcnow)
```

## Next Steps

1. Choose your deployment option based on your needs
2. Set up the basic system with one platform
3. Test thoroughly before adding more platforms
4. Implement monitoring and alerting
5. Scale up gradually
6. Consider advanced features like sentiment analysis and trend detection

## Support and Resources

- **Google Apps Script Documentation**: https://developers.google.com/apps-script
- **Google Sheets API**: https://developers.google.com/sheets/api
- **Trustpilot API**: https://developers.trustpilot.com/
- **Google My Business API**: https://developers.google.com/my-business
- **App Store Connect API**: https://developer.apple.com/app-store-connect/api/
- **Google Play Developer API**: https://developers.google.com/android-publisher
