# Core dependencies
requests>=2.28.0
pandas>=1.5.0
gspread>=5.7.0
google-auth>=2.15.0
google-auth-oauthlib>=0.8.0
google-auth-httplib2>=0.1.0

# Sentiment analysis
textblob>=0.17.1
nltk>=3.8

# Scheduling
schedule>=1.2.0

# Web framework for webhooks
flask>=2.2.0
gunicorn>=20.1.0

# Data processing
python-dateutil>=2.8.0
pytz>=2022.7

# Logging and monitoring
colorlog>=6.7.0

# Optional: Advanced sentiment analysis
# vaderSentiment>=3.3.2
# transformers>=4.25.0

# Optional: Database support
# sqlalchemy>=1.4.0
# psycopg2-binary>=2.9.0

# Optional: Caching
# redis>=4.5.0

# Optional: Configuration management
# python-dotenv>=0.21.0

# Optional: API rate limiting
# ratelimit>=2.2.1

# Optional: Web scraping (if needed)
# beautifulsoup4>=4.11.0
# selenium>=4.7.0

# Optional: Email notifications
# sendgrid>=6.9.0
# smtplib (built-in)

# Optional: Slack notifications (already covered by requests)
# slack-sdk>=3.19.0

# Optional: Discord notifications (already covered by requests)
# discord.py>=2.1.0

# Development dependencies (uncomment if needed)
# pytest>=7.2.0
# pytest-cov>=4.0.0
# black>=22.12.0
# flake8>=6.0.0
# mypy>=0.991
